---
title: Vue中key的作用与原理
date: 2022-05-29
sidebar: 'auto'
categories:
 - 前端笔记
tags:
 - key
---
## 虚拟DOM中key的作用

- key是虚拟DOM对象的标识，当数据发生变化时，Vue会根据新数据生成新的虚拟DOM，随后Vue进行新虚拟DOM与旧虚拟DOM的差异比较。

## 对比规则

- 旧虚拟DOM中找到了与新虚拟DOM相同的key：
  1. 若虚拟DOM中内容没变，直接使用之前的真实DOM
  2. 若虚拟DOM中内容变了，则生成新的真实DOM，随后替换掉页面中之前的真实DOM
- 旧虚拟DOM中未找到与新虚拟DOM相同的key：
  1. 创建新的真实DOM，随后渲染到页面

## 用index作为key可能会引发的问题

1. 若对数据进行逆序添加、逆序删除等破坏顺序的操作，会产生没有必要的真实DOM更新（界面效果没问题，但效率低）
2. 如果结构中还包含输入类的DOM，会产生错误DOM更新（界面有问题）

## 开发中如何选择key

1. 最好使用每条数据的唯一标识作为key，比如id、手机号、身份证号、学号等唯一值。
2. 如果不存在对数据的逆序添加、逆序删除等破坏顺序的操作，仅用于渲染列表用于展示，使用index作为key是没有问题的。