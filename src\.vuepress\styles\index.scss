// place your custom styles here
.vp-blog-mask::after {
  background: #666;
  opacity: 0.2;
}

[data-theme="light"] .transparent-navbar .vp-navbar-start a span,
[data-theme="light"] .transparent-navbar .vp-navbar-center a,
[data-theme="light"] .transparent-navbar .vp-navbar-center .vp-dropdown-subtitle,
[data-theme="light"] .transparent-navbar .vp-navbar-center button,
[data-theme="light"] .transparent-navbar .vp-navbar-end .vp-nav-item svg {
  color: #fff;
}

// 专门处理arrow的background-image问题
[data-theme="light"] .transparent-navbar .vp-dropdown-title .arrow,
[data-theme="light"]
  .transparent-navbar
  .external-link-icon
  .external-link:not(.no-external-link-icon)::after {
  filter: brightness(0) invert(1);
}
.vp-navbar .auto-link {
  padding: 0 0.5rem;
}
