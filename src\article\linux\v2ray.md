---
title: 宝塔面板搭建V2ray
date: 2022-12-05
sidebar: 'auto'
categories:
 - Linux
tags:
 - v2ray
 - 宝塔
 - vps
---

## 准备工作

1. 一个域名
2. 一台vps，并将域名解析到vps
3. 搭建好宝塔并安装nginx
4. 更改vps系统时间

```shell
rm -rf /etc/localtime
ln -s /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
```

## 步骤

- 执行命令

```shell
# bash <(curl -L https://raw.githubusercontent.com/v2fly/fhs-install-v2ray/master/install-release.sh)
```

- 执行完后，修改/usr/local/etc/v2ray下面的config.json文件

```json
{
  "log": {
    "loglevel": "info",
    "access": "/var/log/v2ray/access.log",
    "error": "/var/log/v2ray/error.log"
  },
  "inbounds": [
    {
      "port": 49100,
      "listen":"127.0.0.1",
      "protocol": "vmess",
      "settings": {
        "clients": [
          {
            "id": "d8e19c79-81e1-44ea-b08a-d765b1638334",
            "alterId": 64
          }
        ]
      },
      "streamSettings": {
        "network": "ws",
        "wsSettings": {
        "path": "/happy"
        }
      }
    }
  ],
  "outbounds": [
    {
      "protocol": "freedom",
      "settings": {}
    }
  ]
}
```

- 更换id，通过这个网站可在线生成（https://1024tools.com/uuid）
- 然后宝塔新建一个网站，域名对应一开始解析的
- 申请ssl证书
- 然后在配置文件最顶部加入下面的代码：

```
# 定义变量
map $http_upgrade $connection_upgrade {
  default upgrade;
  ''      close;
}
```

- 添加v2配置

```
#v2配置文件
location /happy {
    proxy_pass       http://127.0.0.1:49100;
    proxy_redirect             off;
    proxy_http_version         1.1;
    proxy_set_header Upgrade   $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host      $http_host;
    }
```

- 保存，回到ssh窗口
- 启动v2ray

​		`systemctl start v2ray`

- 设置开机自启

​		`systemctl enable v2ray`

## v2ray常用命令

```
//启动
systemctl start v2ray

//停止
systemctl stop v2ray

//重启
systemctl restart v2ray

//开机自启
systemctl enable v2ray
```

## 卸载v2ray

- 先停止v2ray

​		`systemctl stop v2ray`

​		`systemctl disable v2ray`

- 再执行一键移除

```bash
# bash <(curl -L https://raw.githubusercontent.com/v2fly/fhs-install-v2ray/master/install-release.sh) --remove
```

## 遇到的问题

所有的步骤执行完后，发现可以ping通，但上不了网。

解决方法：

- 输入systemctl cat v2ray

- 修改v2ray.service文件

- 在[service]区域中，增加

  `Environment="V2RAY_VMESS_AEAD_FORCED=false"`

- 保存并退出

- 更新systemd服务

  `systemctl daemon-reload`

- 重启一下就好了

    `systemctl restart v2ray`



