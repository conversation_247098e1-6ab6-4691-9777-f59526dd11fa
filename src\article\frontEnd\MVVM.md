---
title: MVVM模型和vue数据代理
date: 2022-05-29
sidebar: 'auto'
categories:
 - 前端笔记
tags:
 - MVVM
 - 数据代理
---

## MVVM模型

- M:模型(Model):对应data中的数据
- V:视图(View):模板
- VM:视图模型(ViewModel):Vue实例对象

![image-20220504135033472](https://piccos.yangqifan.top/image-20220504135033472.png?imageSlim)

## 数据代理

### Object.defineProperty()方法

```js
let number = 18
let person = {
  name: '张三',
  sex: '男',
}

Object.defineProperty(person, 'age', {
  // value: 18,
  // enumerable: true, // 控制属性是否可以枚举，默认值为false
  // writable: true, // 控制属性是否可以被修改，默认值为false
  // configurable: true, // 控制属性是否可以被删除，默认值为false
  get() {
    // 当访问该属性时，会调用此函数
    return number
  },
  set(value) {
    // 当属性值被修改时，会调用此函数
    console.log('修改为:'+value)
    number = value
  }
})
console.log(Object.keys(person))
console.log(person)
```

- vue中的数据代理：通过vm对象来代理data对象中属性的操作(读/写)
- 数据代理的好处: 更加方便的操作data中的数据
- 基本原理：通过Object.defineProperty()把data对象中所有属性添加到vm上，为每一个添加到vm上的属性都指定一个setter和getter，在getter/setter内部去操作data中对应的属性

