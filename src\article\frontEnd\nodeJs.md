---
title: Node.js基础
date: 2020-11-29
sidebar: 'auto'
categories:
 - 前端笔记
tags:
 - nodeJs
---
:::tip
Node是一个基于ChromeV8引擎的JavaScript代码运行环境
:::
<!-- more -->
## 模块化开发

* 模块内部可以使用exports对象进行导出，使用require方法导入其他模块

  ```js
  //module-a.js
  const add = (a,b)=>a+b;
  exports.add = add;
  
  //module-b.js
  const a = require('./module-a');
  console.log(a.add(10,20))	//30
  ```

* exports是module.exports的别名，导出对象最终以module.exports为准

## 系统模块

* 系统模块是Node运行环境提供的API

###文件读取操作

```js
const fs = require('fs')
//第一个参数是文件路径，第二个参数是文件编码，第三个参数是一个回调函数
fs.readFile('./demo.js','utf8',(err,doc)=>{
    if(err==null) {
        console.log(doc)
    }
})
```

### 文件写入操作

```js
const fs = require('fs')
//第一个参数是文件路径，第二个参数是要写入的数据，第三个参数是一个回调函数
fs.writeFile('./demo.txt','hello world',err=>{
    if(err!=null) {
        console.log(err)
        return;
    }
    console.log('文件写入成功！')
})
```

### 系统模块path

```js
//进行路径的拼接
//使用__dirname获取当前文件所在的绝对路径
const path = require('path')
const url = path.join('public','upload','images')
console.log(url)	//public\upload\images
```

## 第三方模块

* npm(node package manager):node的第三方模块管理工具

### nodemon

* 监控文件的保存操作

### nrm

* nrm(npm registry manager)：npm下载地址切换工具
* 使用步骤
  1. 使用npm install nrm -g 下载它
  2. 查询可用下载地址列表nrm ls
  3. 切换npm下载地址 nrm use 下载地址名称

### Gulp

* 使用步骤
  1. npm i gulp下载库文件
  2. 在项目根目录下建立gulpfile.js文件
  3. 重构项目文件夹结构，src目录放置源代码文件，dist目录放置构建后文件
  4. 在gulpfile.js中编写任务
  5. 在命令行工具中执行gulp任务

* gulp中提供的方法：
  1. gulp.src():获取任务要处理的文件
  2. gulp.dest():输出文件
  3. gulp.task():建立gulp任务
  4. gulp.watch():监控文件的变化

## package.json

* 项目描述文件，记录了当前项目信息，例如项目名称，版本，作者，github地址，当前项目依赖了哪些第三方模块等，使用npm init -y命令生成

## 创建网站服务器

```js
const http = require('http')	//引用系统模块
const app = http.createServer()	//创建web服务器

//当客户端发送请求的时候
app.on('request',(req,res)=>{
    //req.headers 获取请求报文
    //req.url 获取请求地址
    //req.method 获取请求方法
    res.end('<h1>hello world</h1>')	//响应
})

app.listen(3000)	//监听3000端口
console.log('服务器已启动')

//设置响应头
res.writeHead(200,{
        'content-type':'text/html;charset=utf8'
    })
```

## http状态码

* 200 请求成功
* 404 请求的资源没有被找到
* 500 服务器端错误
* 400 客户端请求有语法错误

## get请求参数

```js
//url模块中的parse方法可以获取到参数
const url = require('url')
const {query,pathname} = url.parse(req.url,true)
```

## post请求参数

```js
const http = require('http')
const app = http.createServer()
const querystring = require('querystring')

app.on('request',(req,res)=>{
    //post参数是通过事件的方式接受的
    //data 当请求参数传递的时候触发data事件
    //end 当参数传递完成的时候触发end事件
    let postParams = '';
    req.on('data',params=>{
        postParams+=params
    })
    req.on('end',()=>{
        console.log(querystring.parse(postParams))
    })
    res.end('ok')
})

app.listen(3000)
console.log('服务器已启动')
```

## 路由

```js
const http = require('http')
const app = http.createServer()
const url = require('url')

app.on('request',(req,res)=>{
const method = req.method.toLowerCase()
const pathname = url.parse(req.url).pathname
res.writeHead(200,{
    'content-type':'text/html;charset=utf8'
})
if(method=='get') {
    if(pathname=='/'||pathname=='/index') {
        res.end('欢迎来到首页')
    }else if(pathname=='/list') {
        res.end('欢迎来到列表页')
    }
}else if(method=='post') {

}
})

app.listen(3000)
console.log('服务器已启动')
```

## 静态文件访问

```js
const http = require('http')
const app = http.createServer()
const url = require('url')
const path = require('path')
const fs = require('fs')

app.on('request',(req,res)=>{
    let pathname = url.parse(req.url).pathname
    let realpath = path.join(__dirname,'public'+pathname)
    fs.readFile(realpath,(error,result)=>{
       if(error!=null) {
        res.end('error')
       }
       res.end(result)
    })
})

app.listen(3000)
console.log('服务器已启动')
```

## 异步函数解决回调地狱

```js
const fs = require('fs')
const promisify = require('util').promisify

const readFile = promisify(fs.readFile)

async function run () {
    let r1 = await readFile('./1.txt','utf8')
    let r2 = await readFile('./2.txt','utf8')
    let r3 = await readFile('./3.txt','utf8')
    console.log(r1)
    console.log(r2)
    console.log(r3)
}
run() //1 2 3
```

## Mongoose第三方包

* 使用Node.js操作MongoDB数据库需要依赖第三方包mongoose
* 使用npm install mongoose下载

```js
//启动mongodb
net start mongoDB
```

## 连接数据库

```js
const mongoose = require('mongoose')
mongoose.connect('mongodb://localhost/nodejs',{useNewUrlParser: true,useUnifiedTopology: true})
        .then(()=>{console.log('数据库连接成功')})
        .catch((err)=>{console.log(err,'数据库连接失败')})
```

## MongoDB增删改查操作

### 创建集合

* 创建集合分为两步，一是对集合设定规则，二是创建集合

```js
//创建集合规则
const courseSchema = new mongoose.Schema({
    name:String,
    author:String,
    isPublished:Boolean
})

//使用规则创建集合
const Course = mongoose.model('Course',courseSchema)
```

### 创建文档

```js
//创建文档
const course = new Course({
    name:'node.js基础',
    author:'洛城小卡',
    isPublished:true
})
//将文档插入到数据库中
course.save()
```

### 创建文档的第二种方式

```js
Course.create({
    name:'Javascript',
    author:'小卡',
    isPublished:false
},(err,res)=>{
    console.log(err)
    console.log(res)
})
```

### 向数据库中导入数据

* mongoimport -d 数据库名称 -c 集合名称 --file 要导入的数据文件

### 查询文档

```js
//find返回数组
User.find({_id:'5c09f236aeb04b22f8460967'}).then((res)=>{
    console.log(res)
})
//findOne返回对象
User.findOne({name:'张三'}).then(result=>{
    console.log(result)
})
//查询年龄大于20并且小于40的文档
User.find({age:{$gt:20,$lt:40}}).then(res=>{
    console.log(res)
})
//查询爱好中有篮球的文档
User.find({hobbies:{$in:['篮球']}}).then(res=>{
    console.log(res)
})
//只查询name和email字段
User.find().select('name email').then(res=>{
    console.log(res)
})
//查询结果不包含_id
User.find().select('name email -_id').then(res=>{
    console.log(res)
})
//根据年龄升序排列
User.find().sort('age').then(res=>{
    console.log(res)
})
//根据年龄降序排列
User.find().sort('-age').then(res=>{
    console.log(res)
})
//跳过前两个文档，限制查询数量为2
User.find().skip(2).limit(2).then(res=>{
    console.log(res)
})
```

### 删除文档

```js
//查找到一条文档并且删除，返回删除的文档
User.findOneAndDelete({_id:'5c09f267aeb04b22f8460968'}).then(res=>{
    console.log(res)
})
//删除多个
User.deleteMany({}).then(res=>{
    console.log(res)
})
```

### 更新文档

```js
//更新集合中的文档
User.updateOne({name:'李四'},{name:'李狗蛋'}).then(res=>{
    console.log(res)
})
// 更新多个文档
User.updateMany({},{age:56}).then(res=>{
    console.log(res)
})
```

## mongoose验证

```js
const userSchema = new mongoose.Schema({
    name:{
        type:String,
        required:[true,'请传入文章标题'],
        minlength:[2,'长度最少为2'],
        maxlength:6,
        trim:true
    },
    age:{
        type:Number,
        max:100,
        min:18
    },
    email:String,
    password:String,
    hobbies:[String],
    publishDate:{
        type:Date,
        default:Date.now
    },
    category:{
      type:String,
      enum:{
        values:['html','css','javascript','node.js'],
        message:'分类名称错误'
      }
    },
    author:{
      type:String,
      validate:{
        validator:(v)=>{
          return v&&v.length>4
        },
        message:'传入的值不符合要求'
      }
    }
})

const User = mongoose.model('User',userSchema)

User.create({
name:'黄海波',
category:'node',
author:'aa'
}).then((res)=>{
    console.log(res)
}).catch(err=>{
  const msg = err.errors
  for(var attr in msg) {
    console.log(msg[attr]['message'])
  }
})
```

## 集合关联

```js
//用户集合
const User = mongoose.model('User',new mongoose.Schema({name:String}))
//文章集合
const Post = mongoose.model('Post',new mongoose.Schema({
    title:String,
    author:{
        type:mongoose.Schema.ObjectId,
        ref:'User'
    }
}))
//联和查询
Post.find().populate('author').then((res)=>{
    console.log(res)
})
```

## 模板引擎

### 下载

```shell
npm install art-template
```

### 模板编译

```js
const template = require('art-template')
const path = require('path')
const views = path.join(__dirname,'views','index.art')
const html = template(views,{
    name:'张三',
    age:20
})
console.log(html)
```

