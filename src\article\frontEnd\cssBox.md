---
title: CSS盒子模型
date: 2023-07-16
sidebar: 'auto'
categories:
 - 前端笔记
tags:
 - 盒模型
 - 行内元素
 - 块元素
 - 行内块元素
---

## CSS长度单位

1. `px`：像素
2. `em`：相对元素`font-size`的倍数
3. `rem`：相对根字体大小（`html`标签）
4. `%`：相对父元素计算

**注意：CSS中设置长度，必须加单位，否则样式无效！**

## 元素的显示模式

### 块元素（block）

又称：块级元素

特点：

1. 在页面中**独占一行**，不会与任何元素共用一行，是从上到下排列的。
2. 默认宽度：撑满**父元素**
3. 默认高度：由**内容**撑开
4. **可以**通过CSS设置宽高

### 行内元素（inline）

又称：内联元素

特点：

1. 在页面中**不独占一行**，一行中不能容纳下的行内元素，会在下一行继续从左到右排列
2. 默认宽度：由**内容**撑开
3. 默认高度：由**内容**撑开
4. **无法**通过CSS设置宽高

### 行内块元素（inline-block）

又称：内联块元素

特点：

1. 在页面中**不独占一行**，一行中不能容纳下的行内元素，会在下一行继续从左到右排列
2. 默认宽度：由**内容**撑开
3. 默认高度：由**内容**撑开
4. **可以**通过CSS设置宽高

**注意：元素早期只分为：行内元素、块级元素，区分条件也只有一条：是否独占一行，如果按照这种分类方式，行内块元素应该算作行内元素**

## 总结各元素的显示模式

### 块元素（block）

1. 主题结构标签：`<html>`、`<body>`
2. 排版标签：`<h1> ~ <h6>`、`<hr>`、`<p>`、`<pre>`、`<div>`
3. 列表标签：`<ul>`、`<ol>`、`<li>`、`<dl>`、`<dt>`、`<dd>`
4. 表格相关标签：`<table>`、`<tbody>`、`<thead>`、`<tfoot>`、`<tr>`、`<caption>`
5. `<form>`与`<option>`

### 行内元素（inline）

1. 文本标签：`<br>`、`<em>`、`<strong>`、`<sup>`、`<sub>`、`<del>`、`<ins>`
2. `<a>`与`<label>`

### 行内块元素（inline-block）

1. 图片：`<img>`
2. 单元格：`<td>`、`<th>`
3. 表单控件：`<input>`、`<textarea>`、`<select>`、`<button>`
4. 框架标签：`<iframe>`

## 盒子模型的组成

`CSS`会把所有的`HTML`元素都看成一个盒子，所有的样式也都是基于这个盒子。

1. margin（外边距）：盒子与外界的距离
2. border（边框）：盒子的边框
3. padding（内边距）：紧贴内容的补白区域
4. content（内容）：元素中的文本或后代元素都是它的内容

图示如下：

<img src="https://piccos.yangqifan.top/box.png?imageSlim" alt="box" style="zoom:50%;" />

盒子的大小= `content` + 左右`padding` + `左右border`

**注意：外边距`margin`不会影响盒子的大小，但会影响盒子的位置。**

## 关于默认宽度

所谓的默认宽度，就是**不设置**`width`属性时，元素所呈现出来的宽度

```
总宽度 = 父的content - 自身的左右margin

内容区的宽度 = 父的content - 自身的左右margin = 自身的左右border - 自身的左右padding
```

## 盒子内边距（padding）

| CSS属性名      | 功能     | 属性值                 |
| :------------- | -------- | ---------------------- |
| padding-top    | 上内边距 | 长度                   |
| padding-right  | 右内边距 | 长度                   |
| padding-bottom | 下内边距 | 长度                   |
| padding-left   | 左内边距 | 长度                   |
| padding        | 复合属性 | 长度，可以设置1～4个值 |

`padding`复合属性的使用规则：

1. `padding:10px;`四个方向内边距都是10px
2. `padding:10px 20px;`（上下、左右）
3. `padding: 10px 20px 30px;`（上、左右、下）
4. `padding: 10px 20px 30px 40px;`（上、右、下、左）

- 注意点：
  1. `padding`的值不能为负数
  2. **行内元素**的左右内边距是没问题的，上下内边距不能完美的设置
  3. **块级元素**、**行内块元素**，四个方向**内边距**都可以完美设置

## 盒子边框（border）

| CSS属性名    | 功能                                   | 属性值                                                       |
| ------------ | -------------------------------------- | ------------------------------------------------------------ |
| border-style | 边框线风格（复合了四个方向的边框风格） | `none`：属性值<br>`solid`：实线<br>`dashed`：虚线<br>`dotted`：点线<br>`double`：双实线<br>...... |
| border-width | 边框线宽度（复合了四个方向的边框宽度） | 长度，默认3px                                                |
| border-color | 边框线颜色（复合了四个方向的边框颜色） | 颜色，默认黑色                                               |
| border       | 复合属性                               | 值没有顺序和数量要求                                         |

## 盒子外边距-margin

| CSS属性名     | 功能                                                | 属性值        |
| ------------- | --------------------------------------------------- | ------------- |
| margin-left   | 左外边距                                            | css中的长度值 |
| margin-right  | 右外边距                                            | css中的长度值 |
| margin-top    | 上外边距                                            | css中的长度值 |
| margin-bottom | 下外边距                                            | css中的长度值 |
| margin        | 复合属性，可以写1～4个值，规律同`padding`（顺时针） | css中的长度值 |

### margin注意事项

1. 子元素的`margin`，是参考父元素的`content`计算的。
2. 上`margin`、左`margin`：影响自己的位置；下`margin`、右`margin`：影响后面兄弟元素的位置
3. 块级元素、行内块元素，均可以完美地设置四个方向的`margin`;但行内元素，左右`margin`可以完美设置，上下`margin`设置无效
4. `margin`的值也可以是`auto`，如果给一个块级元素左右都设置`margin`为`auto`，该块级元素会在父元素中水平居中
5. `margin`的值可以是负值

### margin塌陷问题

`margin`塌陷：第一个子元素的上`margin`会作用在父元素上，最后一个子元素的下`margin`会作用在父元素上。

解决方案：

1. 给父元素设置不为0的`padding`
2. 给父元素设置宽度不为0的`border`
3. 给父元素设置css样式`overflow:hidden`

### margin合并问题

`margin`合并：上面兄弟的下外边距和下面兄弟的上外边距会合并，取一个最大值，而不是相加

## 处理内容溢出

| CSS属性名  | 功能                       | 属性值                                                       |
| ---------- | -------------------------- | ------------------------------------------------------------ |
| overflow   | 溢出内容的处理方式         | `visible`:显示（默认值）<br>`hidden`:隐藏<br>`scroll`:显示滚动条，不论内容是否溢出<br>`auto`:自动显示滚动条，内容不溢出就不显示 |
| overflow-x | 水平方向溢出内容的处理方式 | 同上                                                         |
| overflow-y | 垂直方向溢出内容的处理方式 | 同上                                                         |

注意：

1. `overflow-x`、`overflow-y`不能一个是`hidden`，一个是`visible`，是实验属性，不建议使用
2. `overflow`常用的值是`hidden`和`auto`，除了能处理溢出的显示方式，还可以解决很多疑难杂症

## 隐藏元素的方式

1. `visibility`属性默认值是`show`，如果设置为`hidden`，元素会隐藏（隐藏后占位）
2. 设置`display:none`，就可以让元素隐藏（隐藏后不占位）

## 样式的继承

有些样式会继承，元素如果本身设置了某个样式，就使用本身设置的样式；但如果本身没有设置某个样式，会从父元素开始一级一级继承（优先继承离得近的祖先元素）

### 会继承的css属性

```
字体属性、文本属性（除了vertical-align）、文字颜色等
```

### 不会继承的css属性

```
边框、背景、内边距、外边距、宽高、溢出方式等
```

- 一个规律：能继承的属性，都是不影响布局的，简单说：都是和盒子模型没关系的。

## 默认样式

元素的默认样式优先级**大于**继承的样式，所以如果要重置元素的默认样式，选择器一定要直接选择到该元素

## 布局小技巧

行内元素、行内块元素，可以被父元素当作文本处理。即：可以像处理文本对齐一样，去处理行内、行内块在父元素中的对齐。

例如：`text-align`、`line-height`、`text-indent`等。

## 元素之间的空白问题

产生的原因：行内元素、行内块元素，彼此之间的换行会被浏览器解析为一个空白字符。

解决方案：

1. 去掉换行和空格（不推荐）
2. 给父元素设置`font-size:0`，再给需要显示文字的元素单独设置字体大小（推荐）。

## 行内块的幽灵空白问题

产生原因：行内块元素与文本的基线对齐，而文本的基线与文本最底端之间是有一定距离的。

解决方案：

1. 给行内块设置`vertical`，值不为`baseline`即可，设置为`middle`、`bottom`、`top`均可。
2. 若元素中只有一张图片，设置图片为`display:block`。
3. 给父元素设置`font-size:0`。

