---
title: js高级
date: 2020-11-29
sidebar: 'auto'
categories:
 - 前端笔记
tags:
 - js高级
 - prototype
 - 原型链
---

## 类和对象的关系

* 类：就是一个模具

* 对象：使用模具制造出来的具体产品

* 在类中包含了属性和方法

  * 属性：变量，一般将静态的数据保存为属性

  * 方法：函数，一般将动态的操作封装为方法

## 创建类    

``` javascript
//创建类class
       class Lakers {
           //类的共有属性放到constructor里面
            constructor(name, position) {
                this.name = name
                this.position = position
            }
            action(movement) {
                console.log(this.name+'发动了'+movement)
            }
        }

        var james = new Lakers('勒布朗·詹姆斯', 'SF')
        var kobe = new Lakers('科比·布莱恩特', 'SG')
        console.log(james.name, james.position)
        console.log(kobe.name, kobe.position)
        james.action('妙传天王')
        kobe.action('三分射手')
```
## 类的继承

``` javascript
 class Father {
            constructor(x,y) {
                this.x = x
                this.y = y
            }
            sum() {
                console.log(this.x+this.y)
            }
        }

        class Son extends Father {
            constructor(x,y) {
                super(x,y)
            }
        }
        var son = new Son(1,2);
        son.sum()
```
* super关键字：用于访问和调用对象父类上的函数。可以调用父类的构造函数，也可以调用父类的普通函数。

* super必须在子类this之前调用

## 使用类的注意点

1. 在ES6中类没有变量提升，必须先定义类，再实例化对象。
2. 类里面的共有属性和方法一定要加this使用。
3. 在constructor中this指向创建的实例对象，在方法中this指向它的调用者。

## 创建对象

1. 利用new Object()创建对象`var obj = new Object()`

2. 利用对象字面量创建对象`var obj = {}`

3. 利用构造函数创建对象

   ``` javascript
    function Star(uname,age) {
               this.name = uname
               this.age = age
               this.sing = function() {
                   console.log('我会唱歌')
               }
           }
   
           var ldh = new Star('刘德华',18)
           console.log(ldh)
           ldh.sing()
   ```

   

## 实例成员和静态成员

* 构造函数中的属性和方法称为成员，成员分为两大类。

1. 实例成员：构造函数内部通过this添加的成员，只能通过实例化对象来访问。

   ``` javascript
   console.log(ldh.name)
   ```

2. 静态成员：在构造函数本身上添加的成员，不能通过实例化对象来访问。

   ``` javascript
   Star.sex='男'
   console.log(Star.sex)//静态成员只能通过构造函数来访问
   ```

## 原型对象prototype

* JavaScript规定，每一个构造函数都有一个prototype属性，指向另一个对象。它的作用是共享方法，这样不会造成内存的浪费。

  ```js
  Star.prototype.sing = function() {
      console.log('我会唱歌')
  }
  ```

  

## 对象原型\_\_proto\_\_

* 对象都会有一个属性\_\_proto\_\_指向构造函数的prototype原型对象。
* \_\_proto\_\_对象原型和原型对象prototype是等价的
* \_\_proto\_\_只是为对象的查找机制提供一个路线，它是一个非标准属性，在实际开发中不可以使用这个属性，它只是内部指向原型对象prototype

![对象原型](https://piccos.yangqifan.top/jsAdvanced1.png?imageSlim)

## constructor构造函数

* 对象原型\_\_proto\_\_和原型对象prototype里面都有一个constructor属性，它指回构造函数本身。
* constructor主要记录该对象引用于哪个构造函数。

## 原型链

![原型链](https://piccos.yangqifan.top/jsAdvanced2.png?imageSlim)

## call()

* 两个作用：
  1. 调用方法
  2. 改变this指向

``` javascript
fun.call(thisArg,arg1,arg2,...)
```

## ES5中新增的方法

### 数组方法

#### forEach()

```js
	    //遍历数组，value是数组当前项的值，index是索引，array是数组本身    
		var arr = [1,2,3,4]
        var sum = 0
        arr.forEach(function(value,index,array) {
            sum += value
        })
        console.log(sum)
```

####  filter()

```js
        //主要用于筛选数组，返回的是一个新数组
		var arr = [2, 23, 24, 60, 80]
        var newArr = arr.filter(function (value, index, array) {
            return value > 20
        })
        console.log(newArr)
```

#### some()

```js
		//用于查找数组中是否有满足条件的元素，返回值是boolean，如果找到则终止循环
		var arr = [2, 23, 24, 60, 80]
        var flag = arr.some(function (value) {
            return value > 60
        })
        console.log(flag)
```

#### map()

```js
        //对数组中的每个单元进行操作并返回一个新的数组
		var arr = [2, 5, 8, 10, 12]
        var newArr = arr.map(function (value, index) {
            return value * 2
        })
        console.log(newArr)//[4, 10, 16, 20, 24]
```

#### every()

```js
		//检测数组中的每个元素是否都满足条件，返回值是boolean
		var arr = [2, 5, 8, 10, 12]
        var flag = arr.every(function (value, index) {
            return value > 1
        })
        console.log(flag) //true
```

### 字符串方法

#### trim()

```js
        //删除字符串两边的空白字符，返回一个新的字符串
		var str = '   YANG   '
        var str1 = str.trim()
        console.log(str1)	//'YANG'
```

### 对象方法

#### Object.keys()

```js
        //获取对象的所有属性，返回一个数组
		var obj = {
            id:'1',
            name:'小米10',
            price:3999,
            num:10000
        }
        var arr = Object.keys(obj)
        console.log(arr)// ["id", "name", "price", "num"]
```

##  函数的定义方式

1.  function关键字（命名函数）

   ```js
   function fn() { }
   ```

2. 函数表达式（匿名函数）

   ```js
   var fn = function() {}
   ```

3. new Function()

   ```js
   var fn = new Function('参数1','参数2'..., '函数体')
   ```

* 所有函数都是Function的实例（对象）
* 函数也属于对象

## 函数的调用方式

1. 普通函数

   ```js
           function fn() {
               console.log('函数的调用')
           }
           fn()
   ```

   

2. 对象的方法

   ```js
           var obj = {
               sayHi:function() {
                   console.log('函数的调用')
               }
           }
           obj.sayHi()
   ```

   

3. 构造函数

   ```js
           function Star() {}
           new Star()
   ```

   

4. 绑定事件函数

   ```js
           btn.onclick = function() {
               console.log('函数的调用')
           }
   ```

   

5. 定时器函数

   ```js
   		setInterval(function() {},1000)
   ```

   

6. 立即执行函数

   ```js
           (function() {
               console.log('函数的调用')
           })()
   ```

## 函数内this的指向

* 一般指向调用者

| 调用方式     | this指向       |
| ------------ | -------------- |
| 普通函数调用 | window         |
| 构造函数调用 | 实例对象       |
| 对象方法调用 | 该方法所属对象 |
| 事件绑定方法 | 绑定事件对象   |
| 定时器函数   | window         |
| 立即执行函数 | window         |

## call、apply、bind

* 相同点：都可以改变函数内部this指向
* 不同点：
  1. call和apply会调用函数，bind返回一个改造过的原函数的拷贝
  2. apply传递的参数是数组格式
* 主要应用场景：
  1. call经常做继承
  2. apply经常跟数组有关系，比如借助数学对象求数组最大值最小值
  3. bind可以用来改变定时器内部的this指向

## 严格模式

* 声明"use strict" 开启严格模式
* 严格模式中的变化：
  1. 变量必须先声明，再使用
  2. 不能删除变量
  3. 严格模式下全局作用域中的函数的this是undefined，不是原来的window
  4. 函数不能有重名的参数
  5. 不允许在非函数的代码块内声明函数

## 高阶函数

* 高阶函数是对其他函数进行操作的函数，它接收函数作为参数或将函数作为返回值输出

## 闭包

* 闭包指有权访问另一个函数作用域中变量的函数
* 闭包的作用：延伸变量的作用范围

## 递归

* 如果一个函数在内部可以调用其本身，那么这个函数就是递归函数
* 由于递归很容易发生"栈溢出"错误，所以必须要加退出条件return

## ES6

### let关键字

* 特性：

  1. let声明的关键字只在所处的块级有效

  2. 不存在变量提升（变量必须先声明，再使用）

  3. 暂时性死区

     ```js
           var num = 10
           if(true) {
               console.log(num)
               let num = 100
           }
     ```

### const关键字

* 作用：声明常量
* 特性：
  1. 具有块级作用域
  2. 声明常量时必须赋值
  3. 常量赋值后，值不能更改

### 解构赋值

#### 数组解构

```js
    let [a,b,c] = [1,2,3]
    console.log(a)//1
    console.log(b)//2
    console.log(c)//3
```

#### 对象解构

```js
      let person = {name:'zs',age:18}
      let {name:myName} = person
      console.log(myName)//zs
```

### 箭头函数

 ```js
()=>{}
const fn = ()=>{}
 ```

* 函数体中只有一句代码，且代码的执行结果就是返回值 ，可以省略大括号
* 形参只有一个，可以省略小括号
* 没有自己的this关键字，箭头函数中的this指向它定义位置中的this

### 剩余参数

```js
     function sum(a,...args) {
      console.log(a)	//1
      console.log(args)	//[2,3]
    }
    sum(1,2,3)
```

### 扩展运算符

* 扩展运算符可以将数组或对象转为用逗号分隔的参数序列

  ```js
      let ary = [1,2,3]
      console.log(...ary) //1 2 3
  ```

* 扩展运算符可以用来合并数组

  ```js
      //方法一
  	let ary1 = [1, 2, 3]
      let ary2 = [4, 5, 6]
      let ary3 = [...ary1, ...ary2]
      console.log(ary3) // [1, 2, 3, 4, 5, 6]
  	
  	//方法二
  	let ary1 = [1, 2, 3]
      let ary2 = [4, 5, 6]
      ary1.push(...ary2)
      console.log(ary1) // [1, 2, 3, 4, 5, 6]
  ```

  

### Array的扩展方法

#### Array.from()

* 将类数组或可遍历对象转换为真正的数组

  ```js
      let arrayLike = {
        '0': 'a',
        '1': 'b',
        '2': 'c',
        length: 3
      }
      let ary = Array.from(arrayLike)
      console.log(ary) //["a", "b", "c"]
  
  	//还可以接收第二个参数
  	let arrayLike = {
        "0": 1,
        "1": 2,
        "length": 2
      }
      let newAry = Array.from(arrayLike, item => item * 2)
      console.log(newAry) // [2, 4]
  ```

#### find()

* 找出第一个符合条件的数组成员，如果没找到返回undefined

  ```js
      let ary = [{
        id: 1,
        name: '张三'
      }, {
        id: 2,
        name: '李四'
      }]
     let target = ary.find(item=>item.id==2)
     console.log(target) // {id: 2, name: "李四"}
  ```

#### findIndex()

* 用于找出第一个符合条件的数组成员的位置，如果没找到返回-1

  ```js
      let ary = [1, 5, 20, 30]
      let index = ary.findIndex(item => item > 10)
      console.log(index) // 2
  ```

#### includes()

* 表示某个数组是否包含给定的值，返回布尔值

  ```js
  [1, 2, 3].includes(2) // true 
  [1, 2, 3].includes(4) // false
  ```

### 模板字符串

* ES6新增的创建字符串的方式，使用反引号定义
* 模板字符串中可以解析变量
* 模板字符串中可以换行
* 模板字符串中可以调用函数

### startsWith()和endsWith()

* startsWith():表示参数字符串是否在原字符串的头部，返回布尔值
* endsWith():表示参数字符串是否在原字符串的尾部，返回布尔值

```js
 let str = 'Hello world!';
 str.startsWith('Hello') // true 
 str.endsWith('!')       // true
```

### repeat()

* 将原字符串重复n次，返回一个新字符串

```js
'x'.repeat(3)      // "xxx" 
'hello'.repeat(2)  // "hellohello"
```

### set数据结构

* ES6提供了新的数据结构set，它类似于数组，但是成员的值都是唯一的，没有重复的值

```js
    const s = new Set([1,2,3,4,4])
    console.log(s) //{1, 2, 3, 4}
```

#### 实例方法

* add(value):添加某个值，返回Set结构本身

* delete(value):删除某个值，返回布尔值，表示删除是否成功
* has(value):返回一个布尔值，表示该值是否为Set成员
* clear():清除所有成员，没有返回值

#### 遍历

* Set结构的实例与数组一样，也有forEach方法，没有返回值

```js
    const s = new Set(['a','b','c'])
    s.forEach(item=>{
      console.log(item)
    }) 
```

