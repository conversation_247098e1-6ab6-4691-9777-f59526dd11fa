---
title: CentOs7上安装Nginx
date: 2021-11-26
sidebar: 'auto'
categories:
 - Linux
tags:
 - linux
 - CentOs
 - nginx
---
## 安装nginx

```
1. 先安装一个工具，叫wget
	yum install wget -y
	
2.备份旧的yum源
	mv /etc/yum.repos.d/CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo.backup
	
3.下载阿里云yum源
	wget -O /etc/yum.repos.d/CentOS-Base.repo https://mirrors.aliyun.com/repo/Centos-7.repo
	
4.下载Epel源
	wget -O /etc/yum.repos.d/epel.repo http://mirrors.aliyun.com/repo/epel-7.repo
	
5. 安装nginx
	yum install nginx -y
	
6. 启动nginx
	 systemctl start nginx
	 
7. 验证nginx是否正确启动
	 ps -ef | grep nginx
	 netstat -tunlp | grep nginx
		
```
## 开启端口

```
检查某端口是否开启 
firewall-cmd --query-port=80/tcp --zone=public  #查询80端口是否开启,自行修改端口号


firewall-cmd --zone=public --add-port=80/tcp --permanent #添加80端口，如需添加其他端口，自行修改端口号
修改完成后需要重启服务器才能生效

查看防火墙状态
systemctl status firewalld

关闭防火墙
systemctl stop firewalld

```

```
rpm -ql nginx | grep conf #查看nginx路径
```